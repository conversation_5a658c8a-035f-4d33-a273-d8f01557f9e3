#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取pb3文件中的视频帧元数据

根据.proto定义提取：
- VideoFrameMetaData: time_ns, frame_number等
- VideoFrameToTimestamp: time_us, frame_nbr
- 对比视频帧时间戳与IMU时间戳

"""

import struct
import numpy as np
from pathlib import Path
from datetime import datetime

def parse_varint(data, offset):
    """解析Protocol Buffers的varint编码"""
    result = 0
    shift = 0
    pos = offset
    
    while pos < len(data):
        byte = data[pos]
        result |= (byte & 0x7F) << shift
        pos += 1
        if (byte & 0x80) == 0:
            break
        shift += 7
        if shift >= 70:  # 支持大数值
            break
    
    return result, pos

def parse_float32(data, offset):
    """解析32位浮点数"""
    if offset + 4 <= len(data):
        return struct.unpack('<f', data[offset:offset+4])[0], offset + 4
    return None, offset

def parse_video_frame_metadata(message_data):
    """
    解析VideoFrameMetaData消息
    """
    frame_data = {}
    offset = 0
    
    while offset < len(message_data) - 5:
        try:
            field_id, new_offset = parse_varint(message_data, offset)
            wire_type = field_id & 0x07
            field_number = field_id >> 3
            
            if field_number == 1 and wire_type == 0:  # time_ns (int64)
                time_ns, offset = parse_varint(message_data, new_offset)
                # 应用相同的时间戳修复（前面加140）
                if len(str(time_ns)) == 16:
                    time_ns = int("140" + str(time_ns))
                frame_data['time_ns'] = time_ns
                
            elif field_number == 2 and wire_type == 0:  # frame_number (int64)
                frame_number, offset = parse_varint(message_data, new_offset)
                frame_data['frame_number'] = frame_number
                
            elif field_number == 3 and wire_type == 0:  # exposure_time_ns (int64)
                exposure_time_ns, offset = parse_varint(message_data, new_offset)
                frame_data['exposure_time_ns'] = exposure_time_ns
                
            elif field_number == 4 and wire_type == 0:  # frame_duration_ns (int64)
                frame_duration_ns, offset = parse_varint(message_data, new_offset)
                frame_data['frame_duration_ns'] = frame_duration_ns
                
            elif field_number == 5 and wire_type == 0:  # frame_readout_ns (int64)
                frame_readout_ns, offset = parse_varint(message_data, new_offset)
                frame_data['frame_readout_ns'] = frame_readout_ns
                
            elif field_number == 6 and wire_type == 0:  # iso (int32)
                iso, offset = parse_varint(message_data, new_offset)
                frame_data['iso'] = iso
                
            elif field_number == 7 and wire_type == 5:  # focal_length_mm (float)
                focal_length, offset = parse_float32(message_data, new_offset)
                frame_data['focal_length_mm'] = focal_length
                
            else:
                # 跳过其他字段
                if wire_type == 0:
                    _, offset = parse_varint(message_data, new_offset)
                elif wire_type == 1:
                    offset = new_offset + 8
                elif wire_type == 2:
                    length, length_offset = parse_varint(message_data, new_offset)
                    offset = length_offset + length
                elif wire_type == 5:
                    offset = new_offset + 4
                else:
                    offset += 1
                    
        except:
            offset += 1
    
    # 验证是否是有效的视频帧数据
    if 'time_ns' in frame_data and 'frame_number' in frame_data:
        return frame_data
    
    return None

def parse_video_frame_to_timestamp(message_data):
    """
    解析VideoFrameToTimestamp消息
    """
    frame_time_data = {}
    offset = 0
    
    while offset < len(message_data) - 5:
        try:
            field_id, new_offset = parse_varint(message_data, offset)
            wire_type = field_id & 0x07
            field_number = field_id >> 3
            
            if field_number == 1 and wire_type == 0:  # time_us (int64)
                time_us, offset = parse_varint(message_data, new_offset)
                frame_time_data['time_us'] = time_us
                
            elif field_number == 2 and wire_type == 0:  # frame_nbr (int64)
                frame_nbr, offset = parse_varint(message_data, new_offset)
                frame_time_data['frame_nbr'] = frame_nbr
                
            else:
                # 跳过其他字段
                if wire_type == 0:
                    _, offset = parse_varint(message_data, new_offset)
                elif wire_type == 1:
                    offset = new_offset + 8
                elif wire_type == 2:
                    length, length_offset = parse_varint(message_data, new_offset)
                    offset = length_offset + length
                elif wire_type == 5:
                    offset = new_offset + 4
                else:
                    offset += 1
                    
        except:
            offset += 1
    
    # 验证是否是有效的帧时间数据
    if 'time_us' in frame_time_data and 'frame_nbr' in frame_time_data:
        return frame_time_data
    
    return None

def extract_video_frame_data(data):
    """
    提取视频帧数据
    """
    print("提取视频帧元数据...")
    
    video_frames = []
    frame_timestamps = []
    
    offset = 0
    
    while offset < len(data) - 10:
        try:
            # 读取字段标识符
            field_id, new_offset = parse_varint(data, offset)
            
            if new_offset - offset > 5:  # varint太长，跳过
                offset += 1
                continue
            
            wire_type = field_id & 0x07
            field_number = field_id >> 3
            
            if wire_type == 2:  # length-delimited（嵌套消息）
                length, length_offset = parse_varint(data, new_offset)
                
                if length > 0 and length < 10000:  # 合理的长度
                    message_data = data[length_offset:length_offset + length]
                    
                    # 尝试解析为VideoFrameMetaData消息
                    frame_data = parse_video_frame_metadata(message_data)
                    if frame_data:
                        video_frames.append(frame_data)
                    
                    # 尝试解析为VideoFrameToTimestamp消息
                    frame_time_data = parse_video_frame_to_timestamp(message_data)
                    if frame_time_data:
                        frame_timestamps.append(frame_time_data)
                
                offset = length_offset + length
            else:
                # 跳过其他类型的字段
                if wire_type == 0:  # varint
                    _, offset = parse_varint(data, new_offset)
                elif wire_type == 1:  # 64-bit
                    offset = new_offset + 8
                elif wire_type == 5:  # 32-bit
                    offset = new_offset + 4
                else:
                    offset += 1
                    
        except:
            offset += 1
    
    print(f"解析到 {len(video_frames)} 个VideoFrameMetaData")
    print(f"解析到 {len(frame_timestamps)} 个VideoFrameToTimestamp")
    
    return video_frames, frame_timestamps

def analyze_video_frame_timestamps(video_frames, frame_timestamps):
    """
    分析视频帧时间戳
    """
    print("\n" + "=" * 60)
    print("视频帧时间戳分析")
    print("=" * 60)
    
    if video_frames:
        print(f"VideoFrameMetaData分析:")

        # 按帧号排序
        sorted_frames = sorted(video_frames, key=lambda x: x.get('frame_number', 0))

        for i, frame in enumerate(sorted_frames[:15]):  # 显示前15帧
            time_ns = frame.get('time_ns', 0)
            frame_number = frame.get('frame_number', 0)

            print(f"  解析序号{i+1}: 时间戳={time_ns} ({len(str(time_ns))}位), 帧号={frame_number}")

            # 尝试转换为日期
            if len(str(time_ns)) == 19:
                try:
                    dt = datetime.fromtimestamp(time_ns / 1e9)
                    print(f"        日期: {dt.strftime('%Y-%m-%d %H:%M:%S.%f')}")
                except:
                    print(f"        日期转换失败")

            # 显示其他元数据
            for key, value in frame.items():
                if key not in ['time_ns', 'frame_number']:
                    print(f"        {key}: {value}")

        # 特别分析帧号为0或1的帧
        print(f"\n🔍 寻找真正的第一帧:")
        first_frames = [f for f in video_frames if f.get('frame_number', -1) in [0, 1]]
        if first_frames:
            for frame in first_frames:
                frame_number = frame.get('frame_number', 0)
                time_ns = frame.get('time_ns', 0)
                print(f"  帧号{frame_number}: 时间戳={time_ns}")
                if len(str(time_ns)) == 19:
                    try:
                        dt = datetime.fromtimestamp(time_ns / 1e9)
                        print(f"    日期: {dt.strftime('%Y-%m-%d %H:%M:%S.%f')}")
                    except:
                        pass
        else:
            print("  未找到帧号为0或1的帧")
    
    if frame_timestamps:
        print(f"\nVideoFrameToTimestamp分析:")
        for i, frame_time in enumerate(frame_timestamps[:10]):  # 显示前10个
            time_us = frame_time.get('time_us', 0)
            frame_nbr = frame_time.get('frame_nbr', 0)
            
            print(f"  帧{i+1}: 时间戳={time_us}微秒 ({len(str(time_us))}位), 帧号={frame_nbr}")
            
            # 转换微秒为纳秒进行对比
            time_ns_converted = time_us * 1000
            print(f"        转换为纳秒: {time_ns_converted} ({len(str(time_ns_converted))}位)")
            
            # 尝试转换为日期
            try:
                dt = datetime.fromtimestamp(time_us / 1e6)
                print(f"        日期: {dt.strftime('%Y-%m-%d %H:%M:%S.%f')}")
            except:
                print(f"        日期转换失败")

def compare_with_imu_timestamps(video_frames):
    """
    与IMU时间戳对比
    """
    print("\n" + "=" * 60)
    print("与IMU时间戳对比")
    print("=" * 60)

    # 读取IMU时间戳
    imu_csv = "Metadata/protobuf_euroc_2025_08_22_15_46/data.csv"
    
    if Path(imu_csv).exists():
        import pandas as pd
        # 修复：使用skiprows=1和header=None来正确读取数据
        imu_data = pd.read_csv(imu_csv, skiprows=1, header=None)

        # 显示前几个IMU时间戳
        print(f"IMU时间戳分析:")
        for i in range(min(5, len(imu_data))):
            timestamp = imu_data.iloc[i, 0]
            print(f"  IMU第{i+1}帧: {timestamp} ({len(str(timestamp))}位)")
            try:
                dt = datetime.fromtimestamp(timestamp / 1e9)
                print(f"    日期: {dt.strftime('%Y-%m-%d %H:%M:%S.%f')}")
            except:
                print(f"    日期转换失败")

        # 正确的第一个IMU时间戳
        first_imu_timestamp = imu_data.iloc[0, 0]
        print(f"\n✅ IMU真正的第一个时间戳: {first_imu_timestamp} ({len(str(first_imu_timestamp))}位)")

        try:
            imu_dt = datetime.fromtimestamp(first_imu_timestamp / 1e9)
            print(f"✅ IMU第一帧日期: {imu_dt.strftime('%Y-%m-%d %H:%M:%S.%f')}")
        except:
            print(f"IMU日期转换失败")

        # 计算与视频第一帧的时间差
        if video_frames:
            video_first_frame = sorted(video_frames, key=lambda x: x.get('frame_number', 0))[0]
            video_first_timestamp = video_first_frame.get('time_ns', 0)

            time_diff = video_first_timestamp - first_imu_timestamp
            print(f"\n🕐 时间同步分析:")
            print(f"  视频第一帧时间戳: {video_first_timestamp}")
            print(f"  IMU第一帧时间戳:  {first_imu_timestamp}")
            print(f"  时间差: {time_diff} 纳秒 ≈ {time_diff/1e6:.1f} 毫秒")
            print(f"  视频帧比IMU {'晚' if time_diff > 0 else '早'} {abs(time_diff/1e6):.1f} 毫秒")
    else:
        print("未找到IMU数据文件")

def main():
    """
    主函数
    """
    print("🎬 视频帧元数据提取器")
    print("=" * 60)
    print("基于.proto定义提取VideoFrameMetaData和VideoFrameToTimestamp")
    
    input_file = "Video/video_meta.pb3"
    
    if not Path(input_file).exists():
        print(f"错误：文件不存在 - {input_file}")
        return
    
    with open(input_file, 'rb') as f:
        data = f.read()
    
    try:
        # 提取视频帧数据
        video_frames, frame_timestamps = extract_video_frame_data(data)
        
        if not video_frames and not frame_timestamps:
            print("未找到视频帧元数据")
            return
        
        # 分析视频帧时间戳
        analyze_video_frame_timestamps(video_frames, frame_timestamps)
        
        # 与IMU时间戳对比
        compare_with_imu_timestamps(video_frames)
        
        print(f"\n🎉 视频帧元数据提取完成！")
        print(f"📊 VideoFrameMetaData: {len(video_frames)}个")
        print(f"📊 VideoFrameToTimestamp: {len(frame_timestamps)}个")
        
    except Exception as e:
        print(f"错误：提取过程中发生异常 - {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
