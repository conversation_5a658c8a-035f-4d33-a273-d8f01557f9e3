#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于Protocol Buffers定义的IMU数据解析器

根据提供的.proto定义正确解析IMU数据：
- IMUData: time_ns, gyro[3], accel[3]
- IMUInfo: sample_frequency
- 生成标准EuRoC格式

"""

import struct
import numpy as np
from pathlib import Path
from datetime import datetime

def parse_varint(data, offset):
    """解析Protocol Buffers的varint编码"""
    result = 0
    shift = 0
    pos = offset
    
    while pos < len(data):
        byte = data[pos]
        result |= (byte & 0x7F) << shift
        pos += 1
        if (byte & 0x80) == 0:
            break
        shift += 7
        if shift >= 64:
            break
    
    return result, pos

def parse_float32(data, offset):
    """解析32位浮点数"""
    if offset + 4 <= len(data):
        return struct.unpack('<f', data[offset:offset+4])[0], offset + 4
    return None, offset

def parse_int64(data, offset):
    """解析64位整数"""
    if offset + 8 <= len(data):
        return struct.unpack('<Q', data[offset:offset+8])[0], offset + 8
    return None, offset

def parse_protobuf_imu_data(data):
    """
    解析Protocol Buffers格式的IMU数据
    """
    print("解析Protocol Buffers格式的IMU数据...")
    
    imu_samples = []
    imu_info = {}
    
    offset = 0
    
    while offset < len(data) - 10:
        try:
            # 读取字段标识符
            field_id, new_offset = parse_varint(data, offset)
            
            if new_offset - offset > 5:  # varint太长，跳过
                offset += 1
                continue
            
            wire_type = field_id & 0x07
            field_number = field_id >> 3
            
            if wire_type == 2:  # length-delimited（字符串、字节数组、嵌套消息）
                length, length_offset = parse_varint(data, new_offset)
                
                if length > 0 and length < 10000:  # 合理的长度
                    message_data = data[length_offset:length_offset + length]
                    
                    # 尝试解析为IMUData消息
                    imu_data = parse_imu_data_message(message_data)
                    if imu_data:
                        imu_samples.append(imu_data)
                    
                    # 尝试解析为IMUInfo消息
                    imu_meta = parse_imu_info_message(message_data)
                    if imu_meta:
                        imu_info.update(imu_meta)
                
                offset = length_offset + length
            else:
                # 跳过其他类型的字段
                if wire_type == 0:  # varint
                    _, offset = parse_varint(data, new_offset)
                elif wire_type == 1:  # 64-bit
                    offset = new_offset + 8
                elif wire_type == 5:  # 32-bit
                    offset = new_offset + 4
                else:
                    offset += 1
                    
        except:
            offset += 1
    
    print(f"解析到 {len(imu_samples)} 个IMU样本")
    print(f"IMU信息: {imu_info}")
    
    return imu_samples, imu_info

def parse_imu_data_message(message_data):
    """
    解析IMUData消息
    """
    imu_data = {}
    offset = 0
    
    while offset < len(message_data) - 5:
        try:
            field_id, new_offset = parse_varint(message_data, offset)
            wire_type = field_id & 0x07
            field_number = field_id >> 3
            
            if field_number == 1 and wire_type == 0:  # time_ns (int64)
                time_ns, offset = parse_varint(message_data, new_offset)
                # 将16位时间戳转换为19位标准格式（前面加140）
                if len(str(time_ns)) == 16:
                    time_ns = int("140" + str(time_ns))
                imu_data['time_ns'] = time_ns
                
            elif field_number == 2 and wire_type == 2:  # gyro (repeated float)
                length, length_offset = parse_varint(message_data, new_offset)
                gyro_data = []
                
                for i in range(0, length, 4):
                    if length_offset + i + 4 <= len(message_data):
                        gyro_val, _ = parse_float32(message_data, length_offset + i)
                        if gyro_val is not None:
                            gyro_data.append(gyro_val)
                
                if len(gyro_data) == 3:  # 应该有3个陀螺仪值
                    imu_data['gyro'] = gyro_data
                
                offset = length_offset + length
                
            elif field_number == 4 and wire_type == 2:  # accel (repeated float)
                length, length_offset = parse_varint(message_data, new_offset)
                accel_data = []
                
                for i in range(0, length, 4):
                    if length_offset + i + 4 <= len(message_data):
                        accel_val, _ = parse_float32(message_data, length_offset + i)
                        if accel_val is not None:
                            accel_data.append(accel_val)
                
                if len(accel_data) == 3:  # 应该有3个加速度值
                    imu_data['accel'] = accel_data
                
                offset = length_offset + length
                
            else:
                # 跳过其他字段
                if wire_type == 0:
                    _, offset = parse_varint(message_data, new_offset)
                elif wire_type == 1:
                    offset = new_offset + 8
                elif wire_type == 2:
                    length, length_offset = parse_varint(message_data, new_offset)
                    offset = length_offset + length
                elif wire_type == 5:
                    offset = new_offset + 4
                else:
                    offset += 1
                    
        except:
            offset += 1
    
    # 验证是否是完整的IMU数据
    if 'time_ns' in imu_data and 'gyro' in imu_data and 'accel' in imu_data:
        return imu_data
    
    return None

def parse_imu_info_message(message_data):
    """
    解析IMUInfo消息
    """
    imu_info = {}
    offset = 0
    
    while offset < len(message_data) - 5:
        try:
            field_id, new_offset = parse_varint(message_data, offset)
            wire_type = field_id & 0x07
            field_number = field_id >> 3
            
            if field_number == 5 and wire_type == 5:  # sample_frequency (float)
                freq, offset = parse_float32(message_data, new_offset)
                if freq and 50 < freq < 1000:  # 合理的频率范围
                    imu_info['sample_frequency'] = freq
            else:
                # 跳过其他字段
                if wire_type == 0:
                    _, offset = parse_varint(message_data, new_offset)
                elif wire_type == 1:
                    offset = new_offset + 8
                elif wire_type == 2:
                    length, length_offset = parse_varint(message_data, new_offset)
                    offset = length_offset + length
                elif wire_type == 5:
                    offset = new_offset + 4
                else:
                    offset += 1
                    
        except:
            offset += 1
    
    return imu_info

def filter_and_validate_imu_data(imu_samples):
    """
    过滤和验证IMU数据
    """
    print("过滤和验证IMU数据...")
    
    valid_samples = []
    
    for sample in imu_samples:
        # 验证时间戳
        time_ns = sample['time_ns']
        if not (1e15 < time_ns < 2e18):  # 合理的纳秒时间戳
            continue
        
        # 验证陀螺仪数据
        gyro = sample['gyro']
        if not all(-100 < g < 100 for g in gyro):  # 合理的陀螺仪范围
            continue
        
        # 验证加速度数据
        accel = sample['accel']
        if not all(-100 < a < 100 for a in accel):  # 合理的加速度范围
            continue
        
        valid_samples.append(sample)
    
    print(f"有效样本数: {len(valid_samples)}")
    
    # 按时间戳排序
    valid_samples.sort(key=lambda x: x['time_ns'])
    
    return valid_samples

def analyze_gravity_axis(imu_samples):
    """
    分析重力轴
    """
    print("分析重力轴...")
    
    if len(imu_samples) < 100:
        return None
    
    # 提取加速度数据
    accel_data = np.array([sample['accel'] for sample in imu_samples])
    
    print("加速度轴分析:")
    for i, axis in enumerate(['X', 'Y', 'Z']):
        mean_val = np.mean(accel_data[:, i])
        std_val = np.std(accel_data[:, i])
        
        is_gravity = 5 < abs(mean_val) < 15 and std_val < 5
        marker = " ← 可能的重力轴!" if is_gravity else ""
        
        print(f"  {axis}轴: 均值={mean_val:.3f}, 标准差={std_val:.3f}{marker}")

def save_protobuf_euroc_format(imu_samples, imu_info):
    """
    保存Protocol Buffers解析的EuRoC格式
    """
    print("保存EuRoC格式文件...")
    
    # 创建输出目录
    timestamp_str = datetime.now().strftime("%Y_%m_%d_%H_%M")
    output_dir = Path(f"Metadata/protobuf_euroc_{timestamp_str}")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 保存EuRoC格式文件
    output_file = output_dir / "data.csv"
    
    with open(output_file, 'w', newline='') as f:
        f.write("#timestamp [ns],w_RS_S_x [rad s^-1],w_RS_S_y [rad s^-1],w_RS_S_z [rad s^-1],a_RS_S_x [m s^-2],a_RS_S_y [m s^-2],a_RS_S_z [m s^-2]\n")
        
        for sample in imu_samples:
            time_ns = sample['time_ns']
            gyro = sample['gyro']
            accel = sample['accel']
            
            f.write(f"{time_ns},{gyro[0]:.12f},{gyro[1]:.12f},{gyro[2]:.12f},{accel[0]:.12f},{accel[1]:.12f},{accel[2]:.12f}\n")
    
    # 保存详细报告
    with open(output_dir / "protobuf_conversion_report.txt", 'w') as f:
        f.write("Protocol Buffers IMU数据转换报告\n")
        f.write("=" * 40 + "\n\n")
        f.write(f"数据点数量: {len(imu_samples)}\n")
        
        if imu_samples:
            duration_ns = imu_samples[-1]['time_ns'] - imu_samples[0]['time_ns']
            duration_s = duration_ns / 1e9
            actual_freq = len(imu_samples) / duration_s
            
            f.write(f"持续时间: {duration_s:.2f} 秒\n")
            f.write(f"实际频率: {actual_freq:.2f} Hz\n")
        
        if 'sample_frequency' in imu_info:
            f.write(f"声明频率: {imu_info['sample_frequency']:.2f} Hz\n")
        
        f.write(f"IMU信息: {imu_info}\n")
        
        if imu_samples:
            gyro_data = np.array([sample['gyro'] for sample in imu_samples])
            accel_data = np.array([sample['accel'] for sample in imu_samples])
            
            f.write(f"\n角速度统计 (rad/s):\n")
            for i, axis in enumerate(['X', 'Y', 'Z']):
                f.write(f"  {axis}轴: {gyro_data[:, i].min():.6f} ~ {gyro_data[:, i].max():.6f}, 均值: {gyro_data[:, i].mean():.6f}\n")
            
            f.write(f"\n加速度统计 (m/s²):\n")
            for i, axis in enumerate(['X', 'Y', 'Z']):
                mean_acc = accel_data[:, i].mean()
                marker = " ← 可能的重力轴" if 5 < abs(mean_acc) < 15 else ""
                f.write(f"  {axis}轴: {accel_data[:, i].min():.6f} ~ {accel_data[:, i].max():.6f}, 均值: {mean_acc:.6f}{marker}\n")
    
    print(f"✓ Protocol Buffers解析结果已保存: {output_file}")
    return output_dir

def main():
    """
    主函数
    """
    print("=" * 60)
    print("Protocol Buffers IMU数据解析器")
    print("=" * 60)
    print("基于提供的.proto定义解析IMU数据")
    
    input_file = "Video/video_meta.pb3"
    
    if not Path(input_file).exists():
        print(f"错误：文件不存在 - {input_file}")
        return
    
    with open(input_file, 'rb') as f:
        data = f.read()
    
    try:
        # 解析Protocol Buffers数据
        imu_samples, imu_info = parse_protobuf_imu_data(data)
        
        if not imu_samples:
            print("未找到有效的IMU数据")
            return
        
        # 过滤和验证数据
        valid_samples = filter_and_validate_imu_data(imu_samples)
        
        if not valid_samples:
            print("未找到有效的IMU样本")
            return
        
        # 分析重力轴
        analyze_gravity_axis(valid_samples)
        
        # 保存结果
        output_dir = save_protobuf_euroc_format(valid_samples, imu_info)
        
        print(f"\n🎉 Protocol Buffers解析完成！")
        print(f"📁 输出目录: {output_dir}")
        print(f"📊 数据点数量: {len(valid_samples)}")
        
        if valid_samples:
            duration_ns = valid_samples[-1]['time_ns'] - valid_samples[0]['time_ns']
            duration_s = duration_ns / 1e9
            actual_freq = len(valid_samples) / duration_s
            print(f"⏱️ 持续时间: {duration_s:.2f} 秒")
            print(f"📈 实际频率: {actual_freq:.2f} Hz")
        
    except Exception as e:
        print(f"错误：解析过程中发生异常 - {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
