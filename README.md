# 视频帧提取与轨迹分析工具集

这个工具集包含视频帧提取、数据处理、轨迹对齐和可视化分析等功能，主要用于SLAM系统的数据准备和轨迹评估。

## 功能特点

- 支持不同频率的视频帧提取（20Hz 或 15Hz）
- 时间戳以纳秒为单位，从指定起始时间戳开始
- 自动创建标准的数据集目录结构
- 生成CSV和TXT时间戳文件
- 自动检测并创建下一个可用的数据集编号

## 可用脚本

### 1. 视频帧提取脚本

#### video_frame_extractor.py
- **功能**：从MP4视频中按20Hz频率提取帧
- **提取频率**：20Hz（每50ms一帧）
- **适用场景**：通用视频帧提取，适合大多数视频
- **时间戳间隔**：50,000,000 纳秒

#### video_frame_extractor_15.py
- **功能**：从MP4视频中按15Hz频率提取帧
- **提取频率**：15Hz（每66.67ms一帧）
- **适用场景**：专门用于15fps的视频，可以提取每一帧
- **时间戳间隔**：66,666,667 纳秒
- **推荐用于**：`Video/video_5_long.mp4` 等15fps视频

### 2. 数据处理脚本

#### convert_to_grayscale.py
- **功能**：将数据集中的彩色图像转换为灰度图像
- **适用数据集**：`Datasets/dataset11`
- **主要特性**：
  - 保持原有目录结构和文件名不变
  - 直接覆盖原始彩色图像文件
  - 自动跳过已经是灰度图的文件
  - 支持 cam0 和 cam1 两个相机目录
  - 提供转换进度显示和结果验证
- **处理目录**：
  - `Datasets/dataset11/mav0/cam0/data/`：相机0的图像文件
  - `Datasets/dataset11/mav0/cam1/data/`：相机1的图像文件
- **使用场景**：
  - SLAM算法通常使用灰度图像进行处理
  - 减少存储空间和处理时间
  - 统一图像格式便于后续分析
- **注意事项**：
  - 操作不可逆，建议提前备份重要数据
  - 自动检测和验证转换结果

#### replace_frame_numbers_with_timestamps.py
- **功能**：将帧编号替换为对应的时间戳
- **输入**：包含帧编号的数据文件和时间戳文件
- **输出**：替换帧编号后的新文件
- **使用场景**：处理SLAM或其他系统输出的轨迹数据

#### replace_frame_numbers_with_timestamps_1.py
- **功能**：将49_slice.txt文件中的帧编号替换为对应的时间戳，并删除气压高度列
- **主要特性**：
  - 读取dataset8.txt中的时间戳列表
  - 将第一列的帧编号按顺序替换为对应的时间戳
  - 删除最后一列的气压高度数据（因为气压高度不准确）
  - 自动处理列数变化（从8列变为7列）
- **输入文件**：
  - `Datasets/dataset8/mav0/cam0/dataset8.txt`：时间戳文件
  - `Datasets/dataset8/mav0/cam0/49_slice.txt`：包含帧编号和气压高度的轨迹数据
- **输出文件**：
  - `Datasets/dataset8/mav0/cam0/49_slice_with_timestamps.txt`：处理后的轨迹数据
- **数据格式变化**：
  - **原始格式**：`帧编号,纬度,经度,高度,俯仰角,滚转角,偏航角,气压高度`
  - **输出格式**：`时间戳,纬度,经度,高度,俯仰角,滚转角,偏航角`
- **使用场景**：处理包含不准确气压高度数据的轨迹文件

#### process_height_pitch_yaw.py
- **功能**：修改轨迹数据中的高度、俯仰角和偏航角
- **处理内容**：
  - 将第一帧高度设为200米，最后一帧设为50米
  - 中间帧高度按线性插值计算
  - 所有俯仰角取负号
  - 所有偏航角取反
- **输入**：`5_slice_with_timestamps.txt`
- **输出**：`5_slice_with_timestamps_height_pitch_yaw.txt`

#### process_height_pitch_yaw_1.py
- **功能**：修改轨迹数据中的高度、俯仰角和偏航角（俯仰角处理方式不同）
- **处理内容**：
  - 将第一帧高度设为200米，最后一帧设为50米
  - 中间帧高度按线性插值计算
  - 所有俯仰角减去30度
  - 所有偏航角取反
- **输入**：`5_slice_with_timestamps.txt`
- **输出**：`5_slice_with_timestamps_height_pitch_yaw_1.txt`
- **与process_height_pitch_yaw.py的区别**：俯仰角处理方式不同（减去30度 vs 取负号）

#### process_pitch_yaw.py
- **功能**：仅修改轨迹数据中的俯仰角和偏航角
- **处理内容**：
  - 所有俯仰角减去30度
  - 所有偏航角取反
  - 不修改高度数据
- **输入**：`Datasets/dataset8/mav0/cam0/49_slice_with_timestamps.txt`
- **输出**：`Datasets/dataset8/mav0/cam0/49_slice_with_timestamps_pitch_yaw.txt`
- **特点**：专注于姿态角调整，保留原始高度数据

#### convert_imu_to_euroc.py
- **功能**：将原始IMU数据转换为EuRoC格式的IMU数据文件
- **主要特性**：
  - 读取时间戳文件和IMU原始数据文件
  - 自动进行单位转换（加速度：g → m/s²，角速度：dps → rad/s）
  - 生成符合EuRoC标准的IMU数据格式
  - 提供详细的转换统计信息和示例
- **输入文件**：
  - `Datasets/dataset17/mav0/cam0/dataset17.txt`：时间戳文件
  - `Datasets/dataset17/mav0/imu0/Video00013.txt`：原始IMU数据文件
- **输出文件**：
  - `Datasets/dataset17/mav0/imu0/data.csv`：EuRoC格式的IMU数据文件
- **数据格式**：
  - **输入格式**：帧编号,经度,纬度,高度,俯仰角,滚转角,偏航角,气压高度,加速度X(g),加速度Y(g),加速度Z(g),角速度X(dps),角速度Y(dps),角速度Z(dps)
  - **输出格式**：#timestamp [ns],w_RS_S_x [rad s^-1],w_RS_S_y [rad s^-1],w_RS_S_z [rad s^-1],a_RS_S_x [m s^-2],a_RS_S_y [m s^-2],a_RS_S_z [m s^-2]
- **转换常数**：
  - 重力加速度：9.80665 m/s²
  - 角度转弧度：π/180 ≈ 0.017453292519943 rad/deg
- **使用场景**：为SLAM系统准备标准格式的IMU数据

#### convert_imu_to_euroc_1.py
- **功能**：将原始IMU数据转换为EuRoC格式，包含坐标系变换（右前上→上右前）
- **主要特性**：
  - 读取时间戳文件和IMU原始数据文件
  - 自动进行单位转换（加速度：g → m/s²，角速度：dps → rad/s）
  - **坐标系变换**：从"右前上"转换为EuRoC的"上右前"坐标系
  - 生成符合EuRoC标准的IMU数据格式
  - 提供详细的转换统计信息和坐标系变换示例
- **输入文件**：
  - `Datasets/dataset20/mav0/cam0/dataset20.txt`：时间戳文件
  - `Datasets/dataset20/mav0/imu0/Video00013.txt`：原始IMU数据文件
- **输出文件**：
  - `Datasets/dataset20/mav0/imu0/data.csv`：EuRoC格式的IMU数据文件
- **坐标系变换**：
  - **原始坐标系**：右前上 (x→右方, y→前方, z→上方)
  - **EuRoC坐标系**：上右前 (x→上方, y→右方, z→前方)
  - **变换公式**：
    * EuRoC_x = 原始_z (上方)
    * EuRoC_y = 原始_x (右方)
    * EuRoC_z = 原始_y (前方)
- **数据格式**：
  - **输入格式**：帧编号,经度,纬度,高度,俯仰角,滚转角,偏航角,气压高度,加速度X(g),加速度Y(g),加速度Z(g),角速度X(dps),角速度Y(dps),角速度Z(dps)
  - **输出格式**：#timestamp [ns],w_RS_S_x [rad s^-1],w_RS_S_y [rad s^-1],w_RS_S_z [rad s^-1],a_RS_S_x [m s^-2],a_RS_S_y [m s^-2],a_RS_S_z [m s^-2]
- **转换常数**：
  - 重力加速度：9.80665 m/s²
  - 角度转弧度：π/180 ≈ 0.017453292519943 rad/deg
- **使用场景**：为SLAM系统准备标准格式的IMU数据，特别适用于需要坐标系变换的场景

#### convert_imu_to_euroc_2.py
- **功能**：将原始IMU数据转换为EuRoC格式，包含坐标系变换（前左上→上右前）
- **主要特性**：
  - 读取时间戳文件和IMU原始数据文件
  - 自动进行单位转换（加速度：g → m/s²，角速度：dps → rad/s）
  - **坐标系变换**：从"前左上"转换为EuRoC的"上右前"坐标系
  - 生成符合EuRoC标准的IMU数据格式
  - 提供详细的转换统计信息和坐标系变换示例
- **输入文件**：
  - `Datasets/dataset18/mav0/cam0/dataset18.txt`：时间戳文件
  - `Datasets/dataset18/mav0/imu0/Video00013.txt`：原始IMU数据文件
- **输出文件**：
  - `Datasets/dataset18/mav0/imu0/data.csv`：EuRoC格式的IMU数据文件
- **坐标系变换**：
  - **原始坐标系**：前左上 (x→前方, y→左方, z→上方)
  - **EuRoC坐标系**：上右前 (x→上方, y→右方, z→前方)
  - **变换公式**：
    * EuRoC_x = 原始_z (上方)
    * EuRoC_y = -原始_y (右方)
    * EuRoC_z = 原始_x (前方)
- **数据格式**：
  - **输入格式**：帧编号,经度,纬度,高度,俯仰角,滚转角,偏航角,气压高度,加速度X(g),加速度Y(g),加速度Z(g),角速度X(dps),角速度Y(dps),角速度Z(dps)
  - **输出格式**：#timestamp [ns],w_RS_S_x [rad s^-1],w_RS_S_y [rad s^-1],w_RS_S_z [rad s^-1],a_RS_S_x [m s^-2],a_RS_S_y [m s^-2],a_RS_S_z [m s^-2]
- **转换常数**：
  - 重力加速度：9.80665 m/s²
  - 角度转弧度：π/180 ≈ 0.017453292519943 rad/deg
- **使用场景**：为SLAM系统准备标准格式的IMU数据，特别适用于"前左上"坐标系的数据转换

#### protobuf_imu_parser.py ⭐ **推荐使用**
- **功能**：基于Protocol Buffers定义解析pb3文件中的IMU数据，生成标准EuRoC格式
- **核心优势**：
  - **原生解析**：直接解析Protocol Buffers二进制格式，无需外部依赖
  - **真实时间戳**：使用pb3文件中的原始纳秒时间戳，确保时间精度
  - **完美匹配**：基于提供的.proto定义，准确解析IMU数据结构
  - **自动重力轴检测**：智能识别X轴为重力轴（8.907 m/s²）
- **设计思路**：
  - **Protocol Buffers解析**：手工实现varint、float32、int64等基础类型解析
  - **消息结构识别**：根据字段编号和wire type识别IMUData和IMUInfo消息
  - **数据验证**：多层验证确保数据合理性（时间戳范围、传感器数值范围）
  - **重力轴分析**：统计分析加速度数据，自动识别重力方向
- **解析的数据结构**：
  ```protobuf
  message IMUData {
    int64 time_ns = 1;           // 纳秒时间戳
    repeated float gyro = 2;     // 陀螺仪数据[3]
    repeated float accel = 4;    // 加速度数据[3]
  }
  message IMUInfo {
    float sample_frequency = 5;  // 采样频率
  }
  ```
- **输入文件**：
  - `Video/video_meta.pb3`：Protocol Buffers格式的视频元数据文件
- **输出文件**：
  - `Metadata/protobuf_euroc_YYYYMMDD_HHMM/data.csv`：标准EuRoC格式IMU数据
  - `Metadata/protobuf_euroc_YYYYMMDD_HHMM/protobuf_conversion_report.txt`：详细转换报告
- **成功案例数据特征**：
  - **数据点数量**：4,433个（42.06秒 × 105.39Hz）
  - **时间精度**：纳秒级真实时间戳
  - **X轴重力**：均值8.907 m/s²，标准差0.337（完美的重力轴）
  - **Y轴水平**：均值-0.089 m/s²，标准差0.273
  - **Z轴部分重力**：均值3.872 m/s²，标准差0.700
  - **角速度范围**：±0.7 rad/s（合理的运动范围）
- **技术特点**：
  - **零依赖解析**：无需protobuf库，纯Python实现
  - **容错性强**：跳过无效数据，继续解析有效部分
  - **内存高效**：流式解析，不一次性加载全部数据
  - **调试友好**：详细的解析日志和错误处理
- **使用场景**：
  - **首选方案**：处理pb3格式的IMU数据文件
  - **高精度要求**：需要原始时间戳和完整数据的场景
  - **SLAM系统**：为ORB-SLAM3等提供标准EuRoC格式IMU数据
  - **研究分析**：需要分析IMU数据质量和特征的场景

#### extract_video_frame_metadata.py ⭐ **配套工具**
- **功能**：提取pb3文件中的视频帧元数据，分析视频-IMU时间同步
- **核心优势**：
  - **完整元数据**：提取VideoFrameMetaData和VideoFrameToTimestamp信息
  - **时间同步分析**：自动计算视频帧与IMU数据的时间差
  - **相机参数提取**：获取曝光时间、ISO、焦距等相机设置
  - **数据验证**：验证视频帧时间戳的正确性和连续性
- **解析的数据结构**：
  ```protobuf
  message VideoFrameMetaData {
    int64 time_ns = 1;           // 纳秒时间戳
    int64 frame_number = 2;      // 帧编号
    int64 exposure_time_ns = 3;  // 曝光时间
    int64 frame_duration_ns = 4; // 帧间隔
    int32 iso = 6;               // ISO感光度
    float focal_length_mm = 7;   // 焦距
  }
  message VideoFrameToTimestamp {
    int64 time_us = 1;           // 微秒时间戳
    int64 frame_nbr = 2;         // 帧编号
  }
  ```
- **输入文件**：
  - `Video/video_meta.pb3`：Protocol Buffers格式的视频元数据文件
- **分析结果**：
  - **视频帧数量**：1,262个帧（30fps，42秒）
  - **第一帧时间戳**：1401837602061877235（19位纳秒格式）
  - **相机参数**：曝光9.8ms，ISO 384，焦距5.56mm
  - **时间同步**：视频帧比IMU晚63.4毫秒（合理的硬件延迟）
- **技术特点**：
  - **时间戳修复**：自动将16位相对时间戳转换为19位标准格式
  - **多格式支持**：同时解析VideoFrameMetaData和VideoFrameToTimestamp
  - **智能验证**：检查帧号连续性和时间戳合理性
  - **详细分析**：提供完整的时间同步和相机参数报告
- **使用场景**：
  - **时间同步验证**：确认视频帧与IMU数据的时间对齐
  - **相机标定**：获取相机内参和拍摄参数
  - **数据质量检查**：验证视频元数据的完整性
  - **多传感器融合**：为视觉-惯性SLAM提供精确的时间戳信息

### 3. 轨迹对齐与分析脚本

#### trajectory_alignment.py
- **功能**：使用evo_ape工具对齐GPS轨迹和SLAM轨迹
- **主要任务**：
  - 将GPS数据转换为TUM格式
  - 将SLAM数据转换为TUM格式
  - 计算比例因子
  - 生成对齐结果
- **输出**：TUM格式轨迹文件和对齐结果

#### calculate_scale_factor.py
- **功能**：计算SLAM轨迹的比例因子
- **方法**：通过比较GPS轨迹和SLAM轨迹的路径长度
- **输出**：比例因子数值和统计信息

### 4. 可视化脚本

#### visualize_trajectories_english.py
- **功能**：与上述相同，但使用英文标签
- **优点**：避免中文字体警告问题
- **输出**：英文版可视化图表

### 5. 图片标记工具

#### image_point_marker.py ⭐ **核心标记工具**
- **功能**：在图片上标记归一化坐标点并显示距离信息
- **核心特性**：
  - **归一化坐标转换**：支持[0,1]范围坐标自动转换为像素坐标
  - **图片旋转功能**：支持逆时针90度旋转，坐标自动变换(x,y)→(y,1-x)
  - **智能标记样式**：绿色小圆点(半径5px) + 距离标签 + 边界检测
  - **灵活输出结构**：自动创建`Labeled_images/{dataset}/`目录结构
- **输入参数**：
  - `--image`: 图片路径
  - `--points`: 点坐标和距离，格式："x1,y1 dist1;x2,y2 dist2;..."
  - `--rotate`: 是否旋转图片（true/false）
  - `--dataset`: 数据集名称
- **坐标系说明**：
  - **原点**：图像左上角
  - **X轴**：向右为正方向
  - **Y轴**：向下为正方向
  - **旋转变换**：(x,y) → (y,1-x)，图片尺寸相应调整
- **使用示例**：
  ```bash
  python3 image_point_marker.py \
    --image "path/to/image.png" \
    --points "0.877,0.913 16.100817;0.690,0.621 11.628675" \
    --rotate true \
    --dataset dataset29
  ```

#### mark_image_example.py ⭐ **便捷使用脚本**
- **功能**：快速运行图片标记示例的便捷包装器
- **工作原理**：
  - 通过`subprocess.run()`调用`image_point_marker.py`
  - 预设了9个示例点的完整数据
  - 提供交互式旋转选择
  - 自动检查图片存在性
- **使用方法**：
  ```bash
  python3 mark_image_example.py
  ```
- **脚本关系说明**：
  - `mark_image_example.py`是便捷包装器，内部调用`image_point_marker.py`
  - `image_point_marker.py`是实际执行标记的核心工具
  - 类似于终端命令：`python3 image_point_marker.py --image xxx --points xxx`
- **预设数据**：包含9个点的完整示例，距离从1.17m到16.10m
- **适用场景**：
  - 快速测试标记功能
  - 验证坐标转换和旋转效果
  - 学习工具使用方法

### 5. 距离分析脚本

#### calculate_drone_map_distances.py
- **功能**：计算指定时间戳的无人机位置与所有地图点的距离
- **主要特性**：
  - 支持可配置的比例因子（默认527.2）
  - 自动创建输出目录
  - 生成详细的距离数据文件
  - 生成多视角可视化图片
- **输入文件**：
  - `f_dataset7_mono.txt`：无人机轨迹数据
  - `mp_dataset-dataset7_mono.txt`：地图点数据
- **输出文件**：
  - `distances_timestamp_N.txt`：距离数据文件
  - `distances_visualization_timestamp_N.png`：可视化图片
- **命令行参数**：
  - `-t, --timestamp_index`：时间戳索引（默认0）
  - `-s, --scale_factor`：比例因子（默认527.2）
  - `-p, --base_path`：数据文件路径
  - `--show_plot`：显示可视化图片


### 6. 辅助脚本

#### check_video_info.py
- **功能**：检查视频文件的基本信息
- **输出信息**：分辨率、帧率、总帧数、时长等

### 7. 单目SLAM尺度恢复脚本

#### scale_distance_records.py
- **功能**：将单目ORB-SLAM3生成的距离记录文件恢复到真实尺度

### 8. 单目+IMU模式精度评估脚本

#### evaluate_mono_imu_accuracy.py
- **功能**：评估单目+IMU SLAM模式相对于单目+GPS对齐结果的精度
- **评估方法**：以单目+GPS对齐结果作为Ground Truth，分析单目+IMU模式的误差

## 完整工作流程

### 典型使用顺序

1. **视频帧提取** → 2. **数据处理** → 3. **轨迹对齐** → 4. **可视化分析** → 5. **距离分析**

### 详细步骤

#### 步骤1：视频帧提取
```bash
# 检查视频信息
python3 check_video_info.py

# 提取视频帧（选择合适的脚本）
python3 video_frame_extractor_15.py  # 对于15fps视频
# 或
python3 video_frame_extractor.py     # 对于其他视频

# 如果需要将彩色图像转换为灰度图像（适用于 dataset11）
python3 convert_to_grayscale.py
```

#### 步骤2：数据处理（可选）
```bash
# 如果需要替换帧编号为时间戳
python3 replace_frame_numbers_with_timestamps.py

# 如果需要替换帧编号为时间戳并删除气压高度列（针对dataset8）
python3 replace_frame_numbers_with_timestamps_1.py

# 如果需要修改高度、俯仰角和偏航角（俯仰角取负号，偏航角取反）
python3 process_height_pitch_yaw.py

# 或者如果需要修改高度、俯仰角和偏航角（俯仰角减去30度，偏航角取反）
python3 process_height_pitch_yaw_1.py

# 如果只需要修改俯仰角和偏航角（针对dataset8，俯仰角减去30度，偏航角取反）
python3 process_pitch_yaw.py

# 如果需要将原始IMU数据转换为EuRoC格式（针对dataset17）
python3 convert_imu_to_euroc.py

# 如果需要将原始IMU数据转换为EuRoC格式并进行坐标系变换（右前上→上右前）
python3 convert_imu_to_euroc_1.py

# 如果需要将原始IMU数据转换为EuRoC格式并进行坐标系变换（前左上→上右前）
python3 convert_imu_to_euroc_2.py

# 如果需要从pb3文件中解析IMU数据（推荐方案）
python3 protobuf_imu_parser.py

# 如果需要提取视频帧元数据并分析时间同步（配套工具，这个脚本需要使用到protobuf_imu_parser.py生成的data.csv文件和原始的pb3文件，所以需要先运行protobuf_imu_parser.py还要修改路径指向生成的data.csv文件）
python3 extract_video_frame_metadata.py

# 如果需要在图片上标记点和距离信息
python3 mark_image_example.py  # 快速使用预设示例数据

# 或者自定义标记
python3 image_point_marker.py \
  --image "path/to/image.png" \
  --points "x1,y1 dist1;x2,y2 dist2" \
  --rotate true/false \
  --dataset dataset_name
```

#### 步骤3：轨迹对齐分析
```bash
# 对齐GPS轨迹和SLAM轨迹，计算比例因子
python3 trajectory_alignment.py

# 或单独计算比例因子
python3 calculate_scale_factor.py
```

#### 步骤4：可视化分析
```bash
# 生成完整的可视化分析（推荐英文版）
python3 visualize_trajectories_english.py
```

#### 步骤5：距离分析（可选）
```bash
# 计算指定时间戳的无人机与地图点距离
python3 calculate_drone_map_distances.py -t 0  # 第1个时间戳

# 使用自定义比例因子
python3 calculate_drone_map_distances.py -t 10 -s 500.0  # 第11个时间戳，比例因子500


## 详细使用方法

### 使用 20Hz 提取器（video_frame_extractor.py）

1. 将你的MP4视频文件放在合适的位置
2. 编辑 `video_frame_extractor.py` 文件中的视频路径：
   ```python
   video_file_path = "Video/your_video.mp4"  # 修改为你的视频文件路径
   ```
3. 运行脚本：
   ```bash
   python3 video_frame_extractor.py
   ```

### 使用 15Hz 提取器（video_frame_extractor_15.py）

1. 将你的MP4视频文件放在合适的位置（默认已配置为 `Video/video_5_long.mp4`）
2. 如需修改视频路径，编辑 `video_frame_extractor_15.py` 文件中的视频路径：
   ```python
   video_file_path = "Video/your_video.mp4"  # 修改为你的视频文件路径
   ```
3. 运行脚本：
   ```bash
   python3 video_frame_extractor_15.py
   ```

## 输出结构

脚本会创建以下目录结构：
```
Datasets/
└── datasetN/
    └── mav0/
        └── cam0/
            ├── data/
            │   ├── 1403636579763555584.png  # 起始时间戳
            │   ├── 1403636579813555584.png  # 20Hz: +50ms
            │   ├── 1403636579830222251.png  # 15Hz: +66.67ms
            │   └── ...
            ├── data.csv
            └── datasetN.txt
```

## 文件说明

- `data/` 目录：包含提取的PNG帧，文件名为对应的时间戳
- `data.csv`：包含时间戳和文件名的映射关系
- `datasetN.txt`：只包含时间戳列表

## 依赖要求

### 基础依赖
- Python 3.6+
- OpenCV (cv2)
- NumPy
- Matplotlib
- Pathlib

### 轨迹分析依赖
- evo (轨迹评估工具)

### 安装依赖

#### 基础依赖安装：
```bash
pip install opencv-python numpy matplotlib
```

#### 轨迹分析依赖安装：
```bash
pip install evo --upgrade --no-binary evo
```

#### 安装图形可视化界面依赖 tkinter
```bash
sudo apt install python3-tk
```


## 时间戳格式

### video_frame_extractor.py (20Hz)
- 起始时间戳：1403636579763555584 纳秒
- 时间间隔：50,000,000 纳秒（50毫秒）
- 频率：20Hz

### video_frame_extractor_15.py (15Hz)
- 起始时间戳：1403636579763555584 纳秒
- 时间间隔：66,666,667 纳秒（66.67毫秒）
- 频率：15Hz

## 注意事项

- 确保视频文件路径正确
- 脚本会自动检测现有的数据集并创建下一个编号
- 如果视频文件不存在，脚本会报错并退出
- **选择合适的提取器**：
  - 对于15fps的视频（如 `video_5_long.mp4`），使用 `video_frame_extractor_15.py` 可以提取每一帧
  - 对于其他帧率的视频，使用 `video_frame_extractor.py` 按20Hz提取

## 输出文件说明

### 视频帧提取输出
- `Datasets/datasetN/mav0/cam0/data/`：提取的PNG帧文件
- `Datasets/datasetN/mav0/cam0/data.csv`：时间戳和文件名映射
- `Datasets/datasetN/mav0/cam0/datasetN.txt`：时间戳列表

### 数据处理输出
- `5_slice_with_timestamps.txt`：替换帧编号后的轨迹数据
- `49_slice_with_timestamps.txt`：替换帧编号并删除气压高度后的轨迹数据
  - 由 `replace_frame_numbers_with_timestamps_1.py` 生成
  - 列数从8列减少到7列（删除气压高度列）
- `5_slice_with_timestamps_height_pitch_yaw.txt`：修改高度、俯仰角和偏航角后的数据
  - 由 `process_height_pitch_yaw.py` 生成
  - 俯仰角取负号，偏航角取反
- `5_slice_with_timestamps_height_pitch_yaw_1.txt`：修改高度、俯仰角和偏航角后的数据
  - 由 `process_height_pitch_yaw_1.py` 生成
  - 俯仰角减去30度，偏航角取反
- `49_slice_with_timestamps_pitch_yaw.txt`：修改俯仰角和偏航角后的数据
  - 由 `process_pitch_yaw.py` 生成
  - 俯仰角减去30度，偏航角取反，保留原始高度
- `data.csv`：EuRoC格式的IMU数据文件
  - 由 `convert_imu_to_euroc.py` 生成
  - 包含时间戳、角速度(rad/s)和加速度(m/s²)数据
  - 符合EuRoC数据集标准格式
- `data.csv`：EuRoC格式的IMU数据文件（含坐标系变换-右前上）
  - 由 `convert_imu_to_euroc_1.py` 生成
  - 包含时间戳、角速度(rad/s)和加速度(m/s²)数据
  - 从"右前上"坐标系变换为EuRoC的"上右前"坐标系
  - 符合EuRoC数据集标准格式
- `data.csv`：EuRoC格式的IMU数据文件（含坐标系变换-前左上）
  - 由 `convert_imu_to_euroc_2.py` 生成
  - 包含时间戳、角速度(rad/s)和加速度(m/s²)数据
  - 从"前左上"坐标系变换为EuRoC的"上右前"坐标系
  - 符合EuRoC数据集标准格式
- `Metadata/protobuf_euroc_YYYYMMDD_HHMM/data.csv`：Protocol Buffers解析的EuRoC格式IMU数据 ⭐ **推荐**
  - 由 `protobuf_imu_parser.py` 生成
  - **真实时间戳**：使用pb3文件中的原始纳秒时间戳
  - **完整数据**：4,433个数据点，42.06秒，105.39Hz
  - **重力轴识别**：X轴为重力轴（8.907 m/s²）
  - **数据质量**：角速度±0.7 rad/s，加速度合理分布
  - 符合EuRoC数据集标准格式，可直接用于SLAM系统
- `Metadata/protobuf_euroc_YYYYMMDD_HHMM/protobuf_conversion_report.txt`：Protocol Buffers转换详细报告
  - 包含数据统计、重力轴分析、频率计算等信息
  - 提供数据质量评估和验证结果

### 视频帧元数据输出
- **extract_video_frame_metadata.py输出**：终端显示的详细分析报告
  - **VideoFrameMetaData分析**：1,262个视频帧的完整元数据
    - 时间戳：19位纳秒格式（修复后）
    - 帧编号：1-1262连续编号
    - 相机参数：曝光时间9.8ms，ISO 384，焦距5.56mm
    - 帧间隔：33.33ms（30fps）
  - **VideoFrameToTimestamp分析**：原始16位微秒时间戳数据
  - **时间同步分析**：
    - 视频第一帧：1401837602061877235（2014-06-04 07:20:02.061877）
    - IMU第一帧：1401837601998501323（2014-06-04 07:20:01.998501）
    - 时间差：63.4毫秒（视频帧比IMU晚，合理的硬件延迟）
  - **数据验证结果**：视频帧时间戳连续性和相机参数一致性检查

### 轨迹对齐输出
- `Datasets/dataset7/mav0/cam0/trajectory_alignment/`：
  - `gps_trajectory.tum`：GPS轨迹（TUM格式）
  - `slam_trajectory.tum`：SLAM轨迹（TUM格式）
  - `ape_results.zip`：APE误差分析结果

### 可视化输出
- `trajectory_comparison.png`：自定义轨迹对比图
- `trajectory_comparison_english.png`：英文版轨迹对比图
- `evo_ape_plot.pdf`：APE误差分析图
- `evo_traj_plot.pdf`：专业轨迹对比图

### 距离分析输出
- `Datasets/dataset7/mav0/cam0/distances/`：
  - `distances_timestamp_N.txt`：指定时间戳的距离数据文件
  - `distances_visualization_timestamp_N.png`：距离可视化图片
  - 包含3D散点图、平面投影、距离分布直方图等

### 图片标记输出
- `Labeled_images/{dataset_name}/`：标记后的图片输出目录
  - **目录结构**：按数据集名称自动组织
  - **文件命名**：保持原始图片文件名不变
  - **标记效果**：
    - 绿色小圆点（半径5像素）标记坐标位置
    - 黑色细边框（1像素）增强可见性
    - 距离标签显示在圆点附近，格式："{distance:.2f}m"
    - 白色半透明背景确保文字清晰可读
    - 智能避免文字超出图片边界
  - **坐标处理**：
    - 支持归一化坐标[0,1]自动转换为像素坐标
    - 支持图片旋转时的坐标自动变换
    - 确保标记点在图片有效范围内
  - **成功案例**：
    - 输入：9个点，距离范围1.17m-16.10m
    - 旋转：600×800 → 800×600
    - 输出：`Labeled_images/dataset29/1401837628795210702.png`

## 关键结果示例

### Protocol Buffers数据提取成功案例

#### protobuf_imu_parser.py提取结果
```
🎉 Protocol Buffers解析完成！
📁 输出目录: Metadata/protobuf_euroc_2025_08_22_15_46
📊 数据点数量: 4,433
⏱️ 持续时间: 42.06 秒
📈 实际频率: 105.39 Hz
🎯 X轴重力轴: 均值=8.907 m/s², 标准差=0.337
```

#### extract_video_frame_metadata.py分析结果
```
🎬 视频帧元数据提取完成！
📊 VideoFrameMetaData: 1,262个
📊 VideoFrameToTimestamp: 1,262个

🕐 时间同步分析:
  视频第一帧时间戳: 1401837602061877235
  IMU第一帧时间戳:  1401837601998501323
  时间差: 63375912 纳秒 ≈ 63.4 毫秒
  视频帧比IMU 晚 63.4 毫秒

📹 相机参数:
  - 曝光时间: 9.8ms
  - 帧间隔: 33.33ms (30fps)
  - ISO: 384
  - 焦距: 5.56mm
```

### 比例因子分析
- **SLAM轨迹比例因子**：约527.2
- **含义**：SLAM输出距离需要乘以527.2才能得到真实米制距离
- **原因**：单目SLAM缺乏深度信息，输出相对尺度

### 误差分析
- **平均误差**：约7.55米
- **均方根误差**：约9.16米
- **最大误差**：约27.66米

### 距离分析示例
- **地图点总数**：2091个
- **最近距离**：约403.5米
- **最远距离**：约787.1米
- **平均距离**：约518.6米
- **应用场景**：可见性分析、传感器范围评估、路径规划等

## 视频信息参考

- `Video/video_5_long.mp4`：15fps, 1920x1080, 17秒, 255帧

## 故障排除

### 常见问题

1. **evo工具未找到**
   - 确保已安装evo：`pip install evo --upgrade --no-binary evo`
   - 检查PATH环境变量

2. **图形界面卡住**
   - 确保系统支持图形界面
   - 尝试使用无图形版本的命令

3. **文件路径错误**
   - 检查相对路径是否正确
   - 确保所需文件存在

### 性能优化

- 对于大型视频文件，考虑分段处理
- 轨迹对齐时，确保两个轨迹有足够的时间重叠
- 可视化时，如果数据点过多，考虑采样显示
- 标签分析时，对于大量文件可以考虑并行处理

## 单目SLAM尺度恢复

### 7. 单目SLAM尺度恢复脚本

#### scale_distance_records.py
- **功能**：将单目ORB-SLAM3生成的距离记录文件恢复到真实尺度
- **输入格式**：`Frame_ID Timestamp Camera_X Camera_Y Camera_Z MapPoint_ID MapPoint_X MapPoint_Y MapPoint_Z Pixel_Coordinates Distance`
- **处理内容**：
  - 无人机坐标 (Camera_X, Camera_Y, Camera_Z) × 比例因子
  - 地图点坐标 (MapPoint_X, MapPoint_Y, MapPoint_Z) × 比例因子
  - 距离 (Distance) × 比例因子
  - 像素坐标和其他信息保持不变
- **默认比例因子**：212.544（可通过参数修改）
- **输出文件**：`distances_dataset-dataset8_mono_scaled.txt`
- **主要特性**：
  - 自动检测和验证文件格式
  - 批量处理大量数据行（支持16000+行）
  - 提供处理进度显示
  - 生成缩放前后对比示例
  - 详细的统计信息报告
- **使用方法**：
  ```bash
  # 使用默认参数
  python3 scale_distance_records.py

  # 自定义参数
  python3 scale_distance_records.py -i input_file.txt -o output_file.txt -s 200.0

  # 显示缩放前后对比
  python3 scale_distance_records.py --show-comparison
  ```
- **参数说明**：
  - `-i, --input`：输入文件路径（默认：`Datasets/dataset8/mav0/cam0/distances_dataset-dataset8_mono.txt`）
  - `-o, --output`：输出文件路径（默认：`Datasets/dataset8/mav0/cam0/distances_dataset-dataset8_mono_scaled.txt`）
  - `-s, --scale`：比例因子（默认：212.544）
  - `--show-comparison`：显示缩放前后的对比示例

#### 尺度恢复原理
- **单目SLAM问题**：由于缺乏深度信息，单目SLAM输出的坐标和距离都是相对尺度
- **比例因子作用**：将SLAM坐标系转换到真实世界坐标系（米制单位）
- **转换公式**：`真实坐标 = SLAM坐标 × 比例因子`
- **应用场景**：
  - 轨迹分析和可视化
  - 距离测量和统计
  - 与其他传感器数据融合
  - 真实世界应用部署

#### 处理结果示例
```
原始数据：
  无人机位置: (0.009066, -0.006980, 0.121427)
  地图点位置: (-0.078782, 0.045650, 0.780879)
  距离: 0.667357

缩放后数据：
  无人机位置: (1.926924, -1.483557, 25.808580) 米
  地图点位置: (-16.744641, 9.702634, 165.971146) 米
  距离: 141.842726 米
```

#### 文件格式说明
- **输入文件**：包含11个字段的距离记录文件
- **输出文件**：相同格式，但坐标和距离已缩放到真实尺度
- **处理统计**：成功处理16,489行数据
- **验证方法**：缩放倍数验证确保转换正确性

### 8. 单目+IMU模式精度评估脚本

#### evaluate_mono_imu_accuracy.py
- **功能**：评估单目+IMU SLAM模式相对于单目+GPS对齐结果的精度
- **评估方法**：以单目+GPS对齐结果作为Ground Truth，分析单目+IMU模式的误差
- **主要特性**：
  - 时间戳匹配（1ms容差）
  - 轨迹提取与7自由度对齐（平移、旋转、尺度）
  - 地图点匹配（基于像素坐标欧氏距离）
  - 轨迹误差分析（RMSE、平均误差、最大误差等）
  - 距离误差分析（绝对误差、相对误差）
  - 自动生成可视化图表和详细报告
- **输入文件**：
  - Ground Truth：`distances_dataset-dataset8_mono_scaled.txt`（单目+GPS对齐结果）
  - 待评估：`distances_dataset-dataset8_mono_1_scaled.txt`（单目+IMU结果）
- **输出内容**：
  - 轨迹对比可视化图（3D轨迹、平面投影、误差分布）
  - 距离误差分析图（散点图、误差分布直方图）
  - JSON格式详细报告
  - 文本格式评估报告
- **参数配置**：
  - 时间戳匹配容差：1ms（可调）
  - 像素坐标匹配阈值：0.02（可调）
  - 自动创建带时间戳的结果文件夹
- **使用方法**：
  ```bash
  # 使用默认参数
  python3 evaluate_mono_imu_accuracy.py

  # 自定义参数
  python3 evaluate_mono_imu_accuracy.py --timestamp_tolerance 0.002 --pixel_threshold 0.03

  # 显示可视化图表
  python3 evaluate_mono_imu_accuracy.py --show_plots
  ```
- **参数说明**：
  - `--gt_file`：Ground Truth文件路径
  - `--imu_file`：待评估文件路径
  - `--base_output_dir`：基础输出目录（默认：`Datasets/dataset8/mav0/cam0`）
  - `--timestamp_tolerance`：时间戳匹配容差（秒，默认：0.001）
  - `--pixel_threshold`：像素坐标匹配阈值（默认：0.02）
  - `--show_plots`：显示可视化图表

#### 评估原理与方法
- **时间戳匹配**：在1ms容差内匹配两个文件的时间戳
- **轨迹对齐**：使用Umeyama算法进行7自由度对齐
- **地图点匹配**：基于归一化像素坐标的欧氏距离匹配
- **误差计算**：
  - 轨迹误差：位置RMSE、平均误差、轨迹长度误差
  - 距离误差：绝对距离误差、相对距离误差
- **坐标系统一**：确保轨迹和地图点使用相同的变换矩阵

#### 输出文件结构
```
Datasets/dataset8/mav0/cam0/mono_imu_evaluation_YYYYMMDD_HHMMSS/
├── mono_imu_trajectory_evaluation.png     # 轨迹对比可视化
├── mono_imu_distance_evaluation.png       # 距离误差分析
├── mono_imu_evaluation_report.json        # JSON格式详细报告
└── mono_imu_evaluation_report.txt         # 文本格式评估报告
```

#### 评估指标说明
- **轨迹RMSE**：均方根位置误差（米）
- **轨迹长度误差**：轨迹长度差异百分比
- **距离RMSE**：距离测量均方根误差（米）
- **相对距离误差**：距离误差相对于真实距离的百分比
- **匹配率**：时间戳和地图点的成功匹配比例
