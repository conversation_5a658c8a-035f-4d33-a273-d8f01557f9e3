#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片点标记脚本

功能：
1. 在图片上标记归一化坐标点
2. 显示距离信息
3. 支持图片旋转（逆时针90度）
4. 输出到指定目录结构

使用方法：
python3 image_point_marker.py --image path/to/image.png --points "0.877,0.913 16.100817;0.690,0.621 11.628675" --rotate true --dataset dataset29

"""

import cv2
import numpy as np
import argparse
import os
from pathlib import Path

def parse_points(points_str):
    """
    解析点坐标字符串
    
    Args:
        points_str: "0.877,0.913 16.100817;0.690,0.621 11.628675;..."
    
    Returns:
        list: [(x_norm, y_norm, distance), ...]
    """
    points = []
    
    if not points_str:
        return points
    
    # 分割多个点（用分号分隔）
    point_groups = points_str.split(';')
    
    for group in point_groups:
        group = group.strip()
        if not group:
            continue
        
        try:
            # 分割坐标和距离
            parts = group.split()
            if len(parts) >= 2:
                # 解析坐标 "0.877,0.913"
                coords = parts[0].split(',')
                if len(coords) == 2:
                    x_norm = float(coords[0])
                    y_norm = float(coords[1])
                    distance = float(parts[1])
                    points.append((x_norm, y_norm, distance))
                    print(f"解析点: ({x_norm:.3f}, {y_norm:.3f}) 距离: {distance:.3f}m")
        except ValueError as e:
            print(f"警告：无法解析点 '{group}': {e}")
    
    return points

def rotate_image_and_points(image, points):
    """
    逆时针旋转图片90度，并相应调整点坐标
    
    Args:
        image: 原始图片
        points: [(x_norm, y_norm, distance), ...]
    
    Returns:
        rotated_image: 旋转后的图片
        rotated_points: 旋转后的点坐标
    """
    # 逆时针旋转90度
    rotated_image = cv2.rotate(image, cv2.ROTATE_90_COUNTERCLOCKWISE)
    
    rotated_points = []
    for x_norm, y_norm, distance in points:
        # 坐标变换：(x, y) -> (y, 1-x)
        new_x_norm = y_norm
        new_y_norm = 1.0 - x_norm
        rotated_points.append((new_x_norm, new_y_norm, distance))
        print(f"旋转坐标: ({x_norm:.3f}, {y_norm:.3f}) -> ({new_x_norm:.3f}, {new_y_norm:.3f})")
    
    return rotated_image, rotated_points

def draw_points_on_image(image, points):
    """
    在图片上绘制点和距离标签
    
    Args:
        image: 图片
        points: [(x_norm, y_norm, distance), ...]
    
    Returns:
        marked_image: 标记后的图片
    """
    marked_image = image.copy()
    height, width = image.shape[:2]
    
    print(f"图片尺寸: {width} x {height}")
    
    for i, (x_norm, y_norm, distance) in enumerate(points):
        # 转换为像素坐标
        x_pixel = int(x_norm * width)
        y_pixel = int(y_norm * height)
        
        print(f"点{i+1}: 归一化({x_norm:.3f}, {y_norm:.3f}) -> 像素({x_pixel}, {y_pixel}) 距离: {distance:.3f}m")
        
        # 确保坐标在图片范围内
        x_pixel = max(0, min(width-1, x_pixel))
        y_pixel = max(0, min(height-1, y_pixel))
        
        # 绘制绿色实心圆点（更小）
        circle_radius = 5
        cv2.circle(marked_image, (x_pixel, y_pixel), circle_radius, (0, 255, 0), -1)

        # 绘制圆点边框（黑色）
        cv2.circle(marked_image, (x_pixel, y_pixel), circle_radius, (0, 0, 0), 1)
        
        # 添加距离标签
        distance_text = f"{distance:.2f}m"
        
        # 计算文字位置（避免超出图片边界）
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.4  # 更小的字体
        font_thickness = 1  # 更细的字体
        
        # 获取文字尺寸
        (text_width, text_height), baseline = cv2.getTextSize(distance_text, font, font_scale, font_thickness)
        
        # 计算文字位置（在圆点右上方）
        text_x = x_pixel + circle_radius + 5
        text_y = y_pixel - circle_radius - 5
        
        # 确保文字不超出图片边界
        if text_x + text_width > width:
            text_x = x_pixel - text_width - circle_radius - 5
        if text_y - text_height < 0:
            text_y = y_pixel + circle_radius + text_height + 5
        
        # 绘制文字背景（白色半透明矩形）
        overlay = marked_image.copy()
        cv2.rectangle(overlay, 
                     (text_x - 2, text_y - text_height - 2), 
                     (text_x + text_width + 2, text_y + baseline + 2), 
                     (255, 255, 255), -1)
        cv2.addWeighted(overlay, 0.7, marked_image, 0.3, 0, marked_image)
        
        # 绘制距离文字（黑色）
        cv2.putText(marked_image, distance_text, (text_x, text_y),
                   font, font_scale, (0, 0, 0), font_thickness)
    
    return marked_image

def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='图片点标记工具')
    parser.add_argument('--image', required=True, help='输入图片路径')
    parser.add_argument('--points', required=True, help='点坐标和距离，格式："x1,y1 dist1;x2,y2 dist2;..."')
    parser.add_argument('--rotate', default='false', help='是否旋转图片（true/false）')
    parser.add_argument('--dataset', required=True, help='数据集名称（如dataset29）')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("图片点标记工具")
    print("=" * 60)
    print(f"输入图片: {args.image}")
    print(f"数据集: {args.dataset}")
    print(f"是否旋转: {args.rotate}")
    
    # 检查输入图片是否存在
    if not os.path.exists(args.image):
        print(f"错误：图片文件不存在 - {args.image}")
        return
    
    # 读取图片
    image = cv2.imread(args.image)
    if image is None:
        print(f"错误：无法读取图片 - {args.image}")
        return
    
    print(f"原始图片尺寸: {image.shape[1]} x {image.shape[0]}")
    
    # 解析点坐标
    points = parse_points(args.points)
    if not points:
        print("错误：未找到有效的点坐标")
        return
    
    print(f"解析到 {len(points)} 个点")
    
    # 是否需要旋转
    if args.rotate.lower() == 'true':
        print("执行图片旋转（逆时针90度）...")
        image, points = rotate_image_and_points(image, points)
        print(f"旋转后图片尺寸: {image.shape[1]} x {image.shape[0]}")
    
    # 在图片上标记点
    marked_image = draw_points_on_image(image, points)
    
    # 创建输出目录
    output_dir = Path("Labeled_images") / args.dataset
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 生成输出文件名
    input_filename = Path(args.image).name
    output_path = output_dir / input_filename
    
    # 保存标记后的图片
    success = cv2.imwrite(str(output_path), marked_image)
    
    if success:
        print(f"\n✅ 标记完成！")
        print(f"📁 输出文件: {output_path}")
        print(f"📊 标记点数: {len(points)}")
        print(f"🖼️ 图片尺寸: {marked_image.shape[1]} x {marked_image.shape[0]}")
    else:
        print(f"❌ 保存图片失败: {output_path}")

if __name__ == "__main__":
    main()
