#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
坐标系变换验证脚本 - 验证右前上到上右前的变换

功能：
- 验证convert_imu_to_euroc_1.py中的坐标系变换是否正确
- 对比原始数据和变换后的数据
- 显示变换前后的数据对比

"""

import csv
import math

def verify_coordinate_transform():
    """
    验证坐标系变换：右前上 → 上右前
    """
    # 文件路径
    original_file = "Datasets/dataset20/mav0/imu0/Video00013.txt"
    converted_file = "Datasets/dataset20/mav0/imu0/data.csv"
    
    # 转换常数
    G_TO_MS2 = 9.80665  # 重力加速度转换常数
    DEG_TO_RAD = math.pi / 180.0  # 角度转弧度转换常数
    
    print("=" * 80)
    print("坐标系变换验证脚本 - 右前上 → 上右前")
    print("=" * 80)
    print(f"原始文件: {original_file}")
    print(f"转换文件: {converted_file}")
    
    # 读取原始数据（前3行）
    print("\n读取原始数据...")
    original_data = []
    with open(original_file, 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        header = next(reader)  # 跳过标题行
        for i, row in enumerate(reader):
            if i >= 3:  # 只读取前3行
                break
            if len(row) >= 14:
                # 提取加速度和角速度数据（第9-14列，索引8-13）
                acc_x = float(row[8])   # 加速度X (g)
                acc_y = float(row[9])   # 加速度Y (g)
                acc_z = float(row[10])  # 加速度Z (g)
                gyro_x = float(row[11]) # 角速度X (dps)
                gyro_y = float(row[12]) # 角速度Y (dps)
                gyro_z = float(row[13]) # 角速度Z (dps)
                original_data.append([acc_x, acc_y, acc_z, gyro_x, gyro_y, gyro_z])
    
    # 读取转换后的数据（前3行）
    print("读取转换后的数据...")
    converted_data = []
    with open(converted_file, 'r') as f:
        next(f)  # 跳过标题行
        for i, line in enumerate(f):
            if i >= 3:  # 只读取前3行
                break
            parts = line.strip().split(',')
            if len(parts) >= 7:
                # EuRoC格式：timestamp,wx,wy,wz,ax,ay,az
                gyro_x = float(parts[1])  # 角速度X (rad/s)
                gyro_y = float(parts[2])  # 角速度Y (rad/s)
                gyro_z = float(parts[3])  # 角速度Z (rad/s)
                acc_x = float(parts[4])   # 加速度X (m/s²)
                acc_y = float(parts[5])   # 加速度Y (m/s²)
                acc_z = float(parts[6])   # 加速度Z (m/s²)
                converted_data.append([acc_x, acc_y, acc_z, gyro_x, gyro_y, gyro_z])
    
    print(f"\n验证前 {len(original_data)} 行数据的坐标系变换...")
    print("\n" + "=" * 80)
    
    # 验证每一行数据
    for i, (orig, conv) in enumerate(zip(original_data, converted_data)):
        print(f"\n第 {i+1} 行数据验证:")
        print("-" * 60)
        
        # 原始数据
        orig_acc_x, orig_acc_y, orig_acc_z = orig[0], orig[1], orig[2]
        orig_gyro_x, orig_gyro_y, orig_gyro_z = orig[3], orig[4], orig[5]
        
        print(f"原始数据（右前上坐标系）:")
        print(f"  加速度(g): X={orig_acc_x:8.6f}, Y={orig_acc_y:8.6f}, Z={orig_acc_z:8.6f}")
        print(f"  角速度(dps): X={orig_gyro_x:8.6f}, Y={orig_gyro_y:8.6f}, Z={orig_gyro_z:8.6f}")
        
        # 手动计算变换
        # 单位转换
        acc_x_ms2 = orig_acc_x * G_TO_MS2
        acc_y_ms2 = orig_acc_y * G_TO_MS2
        acc_z_ms2 = orig_acc_z * G_TO_MS2
        gyro_x_rads = orig_gyro_x * DEG_TO_RAD
        gyro_y_rads = orig_gyro_y * DEG_TO_RAD
        gyro_z_rads = orig_gyro_z * DEG_TO_RAD
        
        # 坐标系变换：右前上 → 上右前
        # EuRoC_x = 原始_z (上方)
        # EuRoC_y = 原始_x (右方)
        # EuRoC_z = 原始_y (前方)
        acc_x_euroc = acc_z_ms2    # EuRoC的x = 原始的z (上方)
        acc_y_euroc = acc_x_ms2    # EuRoC的y = 原始的x (右方)
        acc_z_euroc = acc_y_ms2    # EuRoC的z = 原始的y (前方)
        gyro_x_euroc = gyro_z_rads  # EuRoC的x = 原始的z (上方)
        gyro_y_euroc = gyro_x_rads  # EuRoC的y = 原始的x (右方)
        gyro_z_euroc = gyro_y_rads  # EuRoC的z = 原始的y (前方)
        
        print(f"手动计算结果（上右前坐标系）:")
        print(f"  加速度(m/s²): X={acc_x_euroc:12.6f}, Y={acc_y_euroc:12.6f}, Z={acc_z_euroc:12.6f}")
        print(f"  角速度(rad/s): X={gyro_x_euroc:12.9f}, Y={gyro_y_euroc:12.9f}, Z={gyro_z_euroc:12.9f}")
        
        # 转换后的数据
        conv_acc_x, conv_acc_y, conv_acc_z = conv[0], conv[1], conv[2]
        conv_gyro_x, conv_gyro_y, conv_gyro_z = conv[3], conv[4], conv[5]
        
        print(f"脚本输出结果（上右前坐标系）:")
        print(f"  加速度(m/s²): X={conv_acc_x:12.6f}, Y={conv_acc_y:12.6f}, Z={conv_acc_z:12.6f}")
        print(f"  角速度(rad/s): X={conv_gyro_x:12.9f}, Y={conv_gyro_y:12.9f}, Z={conv_gyro_z:12.9f}")
        
        # 验证是否匹配
        acc_match = (abs(acc_x_euroc - conv_acc_x) < 1e-6 and 
                    abs(acc_y_euroc - conv_acc_y) < 1e-6 and 
                    abs(acc_z_euroc - conv_acc_z) < 1e-6)
        gyro_match = (abs(gyro_x_euroc - conv_gyro_x) < 1e-9 and 
                     abs(gyro_y_euroc - conv_gyro_y) < 1e-9 and 
                     abs(gyro_z_euroc - conv_gyro_z) < 1e-9)
        
        if acc_match and gyro_match:
            print(f"✓ 验证通过：坐标系变换正确")
        else:
            print(f"✗ 验证失败：坐标系变换有误")
            if not acc_match:
                print(f"  加速度误差: X={abs(acc_x_euroc - conv_acc_x):.9f}, Y={abs(acc_y_euroc - conv_acc_y):.9f}, Z={abs(acc_z_euroc - conv_acc_z):.9f}")
            if not gyro_match:
                print(f"  角速度误差: X={abs(gyro_x_euroc - conv_gyro_x):.12f}, Y={abs(gyro_y_euroc - conv_gyro_y):.12f}, Z={abs(gyro_z_euroc - conv_gyro_z):.12f}")
    
    print("\n" + "=" * 80)
    print("坐标系变换说明:")
    print("原始坐标系: 右前上 (x→右方, y→前方, z→上方)")
    print("EuRoC坐标系: 上右前 (x→上方, y→右方, z→前方)")
    print("变换公式:")
    print("  EuRoC_x = 原始_z  (上方)")
    print("  EuRoC_y = 原始_x  (右方)")
    print("  EuRoC_z = 原始_y  (前方)")
    print("=" * 80)

def main():
    """
    主函数
    """
    try:
        verify_coordinate_transform()
    except Exception as e:
        print(f"错误：验证过程中发生异常 - {e}")

if __name__ == "__main__":
    main()
