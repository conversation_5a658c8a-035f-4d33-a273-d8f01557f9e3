#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IMU数据转换脚本 - 将原始IMU数据转换为EuRoC格式

功能：
- 读取时间戳文件和IMU数据文件
- 将加速度从g转换为m/s²
- 将角速度从dps转换为rad/s
- 坐标系变换：从"前左上"转换为EuRoC的"上右前"
- 生成符合EuRoC标准的IMU数据文件

坐标系变换说明：
- 原始坐标系：x指向前方，y指向左方，z指向上方
- EuRoC坐标系：x指向上方，y指向右方，z指向前方
- 变换公式：
  * EuRoC_x = 原始_z (上方)
  * EuRoC_y = -原始_y (右方)
  * EuRoC_z = 原始_x (前方)

"""

import os
import csv
import math
import numpy as np
from pathlib import Path

def convert_imu_to_euroc():
    """
    将IMU数据转换为EuRoC格式
    """
    # 文件路径配置
    timestamp_file = "Datasets/dataset18/mav0/cam0/dataset18.txt"
    imu_data_file = "Datasets/dataset18/mav0/imu0/Video00013.txt"
    output_file = "Datasets/dataset18/mav0/imu0/data.csv"
    
    # 转换常数
    G_TO_MS2 = 9.80665  # 重力加速度转换常数
    DEG_TO_RAD = math.pi / 180.0  # 角度转弧度转换常数
    
    print("开始IMU数据转换...")
    print(f"时间戳文件: {timestamp_file}")
    print(f"IMU数据文件: {imu_data_file}")
    print(f"输出文件: {output_file}")
    
    # 检查输入文件是否存在
    if not os.path.exists(timestamp_file):
        print(f"错误：时间戳文件不存在 - {timestamp_file}")
        return False
    
    if not os.path.exists(imu_data_file):
        print(f"错误：IMU数据文件不存在 - {imu_data_file}")
        return False
    
    # 创建输出目录
    output_dir = os.path.dirname(output_file)
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    try:
        # 读取时间戳文件
        print("读取时间戳文件...")
        timestamps = []
        with open(timestamp_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line:
                    timestamps.append(int(line))
        
        print(f"读取到 {len(timestamps)} 个时间戳")
        
        # 读取IMU数据文件
        print("读取IMU数据文件...")
        imu_data = []
        with open(imu_data_file, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            header = next(reader)  # 跳过标题行
            print(f"IMU数据文件标题: {header}")
            
            for row in reader:
                if len(row) >= 14:  # 确保有足够的列
                    # 提取加速度和角速度数据（第9-14列，索引8-13）
                    try:
                        acc_x = float(row[8])   # 加速度X (g)
                        acc_y = float(row[9])   # 加速度Y (g)
                        acc_z = float(row[10])  # 加速度Z (g)
                        gyro_x = float(row[11]) # 角速度X (dps)
                        gyro_y = float(row[12]) # 角速度Y (dps)
                        gyro_z = float(row[13]) # 角速度Z (dps)
                        
                        imu_data.append([acc_x, acc_y, acc_z, gyro_x, gyro_y, gyro_z])
                    except ValueError as e:
                        print(f"警告：数据转换错误 - {e}")
                        continue
        
        print(f"读取到 {len(imu_data)} 行IMU数据")
        
        # 验证数据行数匹配
        if len(timestamps) != len(imu_data):
            print(f"警告：时间戳数量({len(timestamps)})与IMU数据数量({len(imu_data)})不匹配")
            # 取较小的数量
            min_count = min(len(timestamps), len(imu_data))
            timestamps = timestamps[:min_count]
            imu_data = imu_data[:min_count]
            print(f"将处理前 {min_count} 行数据")
        
        # 转换数据并写入输出文件
        print("转换数据并写入输出文件...")
        with open(output_file, 'w', newline='') as f:
            # 写入EuRoC格式的标题
            f.write("#timestamp [ns],w_RS_S_x [rad s^-1],w_RS_S_y [rad s^-1],w_RS_S_z [rad s^-1],a_RS_S_x [m s^-2],a_RS_S_y [m s^-2],a_RS_S_z [m s^-2]\n")
            
            for i, (timestamp, imu_row) in enumerate(zip(timestamps, imu_data)):
                # 提取原始数据
                acc_x_g, acc_y_g, acc_z_g, gyro_x_dps, gyro_y_dps, gyro_z_dps = imu_row
                
                # 单位转换
                # 角速度：dps → rad/s
                gyro_x_rads = gyro_x_dps * DEG_TO_RAD
                gyro_y_rads = gyro_y_dps * DEG_TO_RAD
                gyro_z_rads = gyro_z_dps * DEG_TO_RAD

                # 加速度：g → m/s²
                acc_x_ms2 = acc_x_g * G_TO_MS2
                acc_y_ms2 = acc_y_g * G_TO_MS2
                acc_z_ms2 = acc_z_g * G_TO_MS2

                # 坐标系变换：从"前左上"转换为EuRoC的"上右前"
                # 原始坐标系：x指向前方，y指向左方，z指向上方
                # EuRoC坐标系：x指向上方，y指向右方，z指向前方
                # 变换公式：
                # EuRoC_x = 原始_z (上方)
                # EuRoC_y = -原始_y (右方 = -左方)
                # EuRoC_z = 原始_x (前方)

                # 加速度坐标系变换
                acc_x_euroc = acc_z_ms2    # EuRoC的x = 原始的z (上方)
                acc_y_euroc = -acc_y_ms2   # EuRoC的y = -原始的y (右方)
                acc_z_euroc = acc_x_ms2    # EuRoC的z = 原始的x (前方)

                # 角速度坐标系变换
                gyro_x_euroc = gyro_z_rads  # EuRoC的x = 原始的z (上方)
                gyro_y_euroc = -gyro_y_rads # EuRoC的y = -原始的y (右方)
                gyro_z_euroc = gyro_x_rads  # EuRoC的z = 原始的x (前方)
                
                # 写入数据行（EuRoC格式：timestamp,wx,wy,wz,ax,ay,az）
                # 使用变换后的EuRoC坐标系数据
                f.write(f"{timestamp},{gyro_x_euroc:.12f},{gyro_y_euroc:.12f},{gyro_z_euroc:.12f},{acc_x_euroc:.12f},{acc_y_euroc:.12f},{acc_z_euroc:.12f}\n")
                
                # 显示进度
                if (i + 1) % 50 == 0 or (i + 1) == len(timestamps):
                    print(f"已处理 {i + 1}/{len(timestamps)} 行数据")
        
        # 输出转换统计信息
        print("\n转换完成！")
        print(f"成功转换 {len(timestamps)} 行数据")
        print(f"输出文件: {output_file}")
        
        # 显示转换示例
        if len(imu_data) > 0:
            print("\n转换示例（第一行数据）：")
            first_imu = imu_data[0]
            first_timestamp = timestamps[0]
            
            print(f"原始数据:")
            print(f"  时间戳: {first_timestamp}")
            print(f"  加速度(g): X={first_imu[0]:.6f}, Y={first_imu[1]:.6f}, Z={first_imu[2]:.6f}")
            print(f"  角速度(dps): X={first_imu[3]:.6f}, Y={first_imu[4]:.6f}, Z={first_imu[5]:.6f}")
            
            # 单位转换后的数据
            gyro_x_rads = first_imu[3] * DEG_TO_RAD
            gyro_y_rads = first_imu[4] * DEG_TO_RAD
            gyro_z_rads = first_imu[5] * DEG_TO_RAD
            acc_x_ms2 = first_imu[0] * G_TO_MS2
            acc_y_ms2 = first_imu[1] * G_TO_MS2
            acc_z_ms2 = first_imu[2] * G_TO_MS2

            print(f"单位转换后数据（原始坐标系-前左上）:")
            print(f"  时间戳: {first_timestamp}")
            print(f"  角速度(rad/s): X={gyro_x_rads:.12f}, Y={gyro_y_rads:.12f}, Z={gyro_z_rads:.12f}")
            print(f"  加速度(m/s²): X={acc_x_ms2:.12f}, Y={acc_y_ms2:.12f}, Z={acc_z_ms2:.12f}")

            # 坐标系变换后的数据（EuRoC格式）
            acc_x_euroc = acc_z_ms2    # EuRoC的x = 原始的z (上方)
            acc_y_euroc = -acc_y_ms2   # EuRoC的y = -原始的y (右方)
            acc_z_euroc = acc_x_ms2    # EuRoC的z = 原始的x (前方)
            gyro_x_euroc = gyro_z_rads  # EuRoC的x = 原始的z (上方)
            gyro_y_euroc = -gyro_y_rads # EuRoC的y = -原始的y (右方)
            gyro_z_euroc = gyro_x_rads  # EuRoC的z = 原始的x (前方)

            print(f"坐标系变换后数据（EuRoC坐标系-上右前）:")
            print(f"  时间戳: {first_timestamp}")
            print(f"  角速度(rad/s): X={gyro_x_euroc:.12f}, Y={gyro_y_euroc:.12f}, Z={gyro_z_euroc:.12f}")
            print(f"  加速度(m/s²): X={acc_x_euroc:.12f}, Y={acc_y_euroc:.12f}, Z={acc_z_euroc:.12f}")
        
        print(f"\n转换常数:")
        print(f"  重力加速度: {G_TO_MS2} m/s²")
        print(f"  角度转弧度: {DEG_TO_RAD:.15f} rad/deg")

        print(f"\n坐标系变换信息:")
        print(f"  原始坐标系: 前左上 (x→前方, y→左方, z→上方)")
        print(f"  EuRoC坐标系: 上右前 (x→上方, y→右方, z→前方)")
        print(f"  变换公式:")
        print(f"    EuRoC_x = 原始_z  (上方)")
        print(f"    EuRoC_y = -原始_y (右方)")
        print(f"    EuRoC_z = 原始_x  (前方)")
        
        return True
        
    except Exception as e:
        print(f"错误：转换过程中发生异常 - {e}")
        return False

def main():
    """
    主函数
    """
    print("=" * 60)
    print("IMU数据转换脚本 - 转换为EuRoC格式")
    print("=" * 60)
    
    success = convert_imu_to_euroc()
    
    if success:
        print("\n✓ IMU数据转换成功完成！")
    else:
        print("\n✗ IMU数据转换失败！")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
