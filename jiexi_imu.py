import struct

SEI_HEADER = b'\x90\xeb'
SEI_TAIL = 0xAA

# 文件路径（修改为你实际路径）
input_file = r"/home/<USER>/sdb1/work/lzq/pysot-master/testvideo0808/Video00007-part-peidianxiang.ts"
output_file = r"/home/<USER>/sdb1/work/lzq/pysot-master/testvideo0808/Video00007-part-peidianxiang.txt"

def remove_emulation_prevention_bytes(data):
    """
    去除 emulation prevention 字节（0x03）
    例如: 00 00 03 → 00 00
    """
    result = bytearray()
    i = 0
    while i < len(data):
        if i + 2 < len(data) and data[i] == 0x00 and data[i+1] == 0x00 and data[i+2] == 0x03:
            result += data[i:i+2]  # 保留前两个 0x00
            i += 3  # 跳过 0x03
        else:
            result.append(data[i])
            i += 1
    return bytes(result)

def parse_sei_payload(payload, f_out, cnt):
    if not payload.startswith(SEI_HEADER) or payload[-1] != SEI_TAIL:
        return

    try:
        (
            frame_len,
            frame_id,
            lon_raw,
            lat_raw,
            height_raw,
            pitch_raw,
            roll_raw,
            yaw_raw,
            baro_raw,  # 新增字段
            accelerate_x,
            accelerate_y,
            accelerate_z,
            angularvelocity_x,
            angularvelocity_y,
            angularvelocity_z
        ) = struct.unpack('<H I i i H i i i H h h h h h h', payload[2:-1])  # 新增2字节

        longitude = lon_raw * 1e-7
        latitude = lat_raw * 1e-7
        height = height_raw * 0.1
        #height = (height_raw * 0.1 - 100) * 10.0
        pitch = pitch_raw * 0.01
        roll = roll_raw * 0.01
        yaw = yaw_raw * 0.01
        baro_height = baro_raw * 0.1  # 新增字段，单位m
        accelerate_x_g = accelerate_x / 2048.0
        accelerate_y_g = accelerate_y / 2048.0
        accelerate_z_g = accelerate_z / 2048.0
        angularvelocity_x_dps = angularvelocity_x / 16.4
        angularvelocity_y_dps = angularvelocity_y / 16.4
        angularvelocity_z_dps = angularvelocity_z / 16.4
        #print("angularvelocity_x:", angularvelocity_x)
        f_out.write(f"{cnt}, {longitude:.7f}, {latitude:.7f}, {height:.1f}, {pitch:.2f}, {roll:.2f}, {yaw:.2f}, {baro_height:.2f}, {accelerate_x_g:.2f}, {accelerate_y_g:.2f}, {accelerate_z_g:.2f}, {angularvelocity_x_dps:.2f}, {angularvelocity_y_dps:.2f}, {angularvelocity_z_dps:.2f}\n")

    except Exception as e:
        print("解析失败:", e)

def extract_sei_from_h265(filename):
    global cnt
    
    with open(filename, 'rb') as f:
        data = f.read()

    with open(output_file, "w", encoding='utf-8') as f_out:
        f_out.write("帧编号,经度,纬度,高度,俯仰角,滚转角,偏航角,气压高度,加速度X,加速度Y,加速度Z,角速度X,角速度Y,角速度Z\n")


        start_code = b'\x00\x00\x00\x01'
        offset = 0

        while True:
            start = data.find(start_code, offset)
            if start == -1:
                break
            end = data.find(start_code, start + 4)
            if end == -1:
                end = len(data)

            nal_unit = data[start + 4:end]
            if len(nal_unit) < 1:
                offset = end
                continue

            nal_header = nal_unit[0]
            nal_type = (nal_header >> 1) & 0x3F

            if nal_type in (39, 40):  # SEI
                # 去掉 NAL 头，剩下的是 SEI 负载（可能有转义字节）
                sei_payload = remove_emulation_prevention_bytes(nal_unit[1:])

                sei_offset = sei_payload.find(SEI_HEADER)
                while sei_offset != -1 and sei_offset + 33 + 12 < len(sei_payload):
                    if sei_payload[sei_offset + 32 + 12] == SEI_TAIL:
                        sei_data = sei_payload[sei_offset:sei_offset + 33 + 12]
                        cnt = cnt + 1
                        #print(cnt)
                        parse_sei_payload(sei_data, f_out, cnt)
                    sei_offset = sei_payload.find(SEI_HEADER, sei_offset + 1)


            offset = end

if __name__ == "__main__":
    cnt = 0
    extract_sei_from_h265(input_file)
    print(f"✅ 解析完成，结果已保存到: {output_file}")