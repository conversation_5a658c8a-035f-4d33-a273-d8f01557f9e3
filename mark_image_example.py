#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片标记示例脚本

直接运行你提供的示例数据
"""

import subprocess
import sys
from pathlib import Path

def main():
    """
    运行图片标记示例
    """
    print("🖼️ 图片点标记示例")
    print("=" * 60)
    
    # 你提供的数据
    image_path = "Datasets/dataset29/mav0/cam0/data/1401837628795210702.png"
    dataset_name = "dataset29"

    # 检查图片是否存在，如果不存在则使用最接近的图片
    if not Path(image_path).exists():
        print(f"⚠️ 指定的图片不存在: {image_path}")
        # 寻找最接近的时间戳
        data_dir = Path("Datasets/dataset29/mav0/cam0/data")
        if data_dir.exists():
            png_files = list(data_dir.glob("*.png"))
            if png_files:
                # 使用第一个找到的图片作为示例
                image_path = str(png_files[0])
                print(f"🔄 使用替代图片: {image_path}")
            else:
                print("❌ 未找到任何PNG图片")
                return
        else:
            print("❌ 数据目录不存在")
            return
    
    # 点坐标数据（用分号分隔多个点）
    points_data = (
        "0.877,0.913 16.100817;"
        "0.849,0.916 13.668390;"
        "0.785,0.592 9.512950;"
        "0.716,0.516 7.869578;"
        "0.517,0.676 5.596085;"
        "0.654,0.480 4.054179;"
        "0.442,0.103 3.065709;"
        "0.654,0.201 2.017197;"
        "0.559,0.417 1.344896"
    )
    
    # 检查图片是否存在
    if not Path(image_path).exists():
        print(f"❌ 错误：图片文件不存在 - {image_path}")
        print("请确保图片文件存在于指定路径")
        return
    
    print(f"📁 输入图片: {image_path}")
    print(f"📊 数据集: {dataset_name}")
    print(f"🎯 标记点数: 9个")
    
    # 询问是否需要旋转
    print(f"\n是否需要旋转图片？")
    print("1. 不旋转 (false)")
    print("2. 逆时针旋转90度 (true)")
    
    while True:
        choice = input("请选择 (1/2): ").strip()
        if choice == "1":
            rotate = "false"
            break
        elif choice == "2":
            rotate = "true"
            break
        else:
            print("请输入 1 或 2")
    
    print(f"🔄 旋转设置: {rotate}")
    
    # 构建命令
    cmd = [
        "python3", "image_point_marker.py",
        "--image", image_path,
        "--points", points_data,
        "--rotate", rotate,
        "--dataset", dataset_name
    ]
    
    print(f"\n🚀 执行标记...")
    print("命令:", " ".join(cmd[:4]) + " --points [数据] --rotate " + rotate + " --dataset " + dataset_name)
    
    try:
        # 运行标记脚本
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        # 显示输出
        if result.stdout:
            print("\n📋 执行输出:")
            print(result.stdout)
        
        if result.stderr:
            print("\n⚠️ 错误信息:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("\n🎉 标记完成！")
            expected_output = f"Labeled_images/{dataset_name}/1401837628795210702.png"
            print(f"📁 输出文件: {expected_output}")
            
            # 检查输出文件是否存在
            if Path(expected_output).exists():
                print("✅ 输出文件已生成")
            else:
                print("❌ 输出文件未找到")
        else:
            print(f"❌ 执行失败，返回码: {result.returncode}")
    
    except Exception as e:
        print(f"❌ 执行过程中发生错误: {e}")

if __name__ == "__main__":
    main()
